import 'package:di/di.dart';

// Example service classes
class ApiService {
  final String baseUrl;
  bool _isDisposed = false;

  ApiService(this.baseUrl);

  void makeRequest(String endpoint) {
    if (_isDisposed) {
      throw StateError('ApiService has been disposed');
    }
    print('Making request to: $baseUrl/$endpoint');
  }

  void dispose() {
    _isDisposed = true;
    print('ApiService disposed');
  }
}

class UserRepository {
  final ApiService _apiService;
  bool _isDisposed = false;

  UserRepository(this._apiService);

  void getUser(String id) {
    if (_isDisposed) {
      throw StateError('UserRepository has been disposed');
    }
    _apiService.makeRequest('users/$id');
  }

  void dispose() {
    _isDisposed = true;
    print('UserRepository disposed');
  }
}

class UserService {
  final UserRepository _repository;
  bool _isDisposed = false;

  UserService(this._repository);

  void fetchUserProfile(String userId) {
    if (_isDisposed) {
      throw StateError('UserService has been disposed');
    }
    print('Fetching user profile for: $userId');
    _repository.getUser(userId);
  }

  void dispose() {
    _isDisposed = true;
    print('UserService disposed');
  }
}

void main() async {
  // Create dependency injection instance
  final di = GetItDependencyInjection();

  // Initialize
  await di.init();
  print('Dependency injection initialized');

  try {
    // Register dependencies with dispose functions
    final apiService = ApiService('https://api.example.com');
    di.register<ApiService>(
      store: apiService,
      dispose: (service) => service.dispose(),
    );

    final userRepository = UserRepository(di.get<ApiService>());
    di.register<UserRepository>(
      store: userRepository,
      dispose: (repo) => repo.dispose(),
    );

    final userService = UserService(di.get<UserRepository>());
    di.register<UserService>(
      store: userService,
      dispose: (service) => service.dispose(),
    );

    print('\nAll dependencies registered successfully!');

    // Use the services
    print('\n--- Using services ---');
    final service = di.get<UserService>();
    service.fetchUserProfile('123');

    // Check what's registered
    print('\n--- Checking registrations ---');
    print('ApiService registered: ${di.has<ApiService>()}');
    print('UserRepository registered: ${di.has<UserRepository>()}');
    print('UserService registered: ${di.has<UserService>()}');

    // Unregister a specific service
    print('\n--- Unregistering UserService ---');
    await di.unregister<UserService>();
    print('UserService registered: ${di.has<UserService>()}');

    // Reset everything
    print('\n--- Resetting all dependencies ---');
    await di.reset();
    print('ApiService registered: ${di.has<ApiService>()}');
    print('UserRepository registered: ${di.has<UserRepository>()}');
    print('UserService registered: ${di.has<UserService>()}');

  } finally {
    // Clean up
    await di.dispose();
    print('\nDependency injection disposed');
  }
}
