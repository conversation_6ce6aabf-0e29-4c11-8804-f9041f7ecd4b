# Dependency Injection (DI) Package

A clean dependency injection abstraction for the BioMedict monorepo, built on top of GetIt.

[![nonstop_cli](https://img.shields.io/badge/started%20with-nonstop_cli-166C4E.svg?style=flat-square)](https://pub.dev/packages/nonstop_cli)
[![melos](https://img.shields.io/badge/maintained%20with-melos-f700ff.svg?style=flat-square)](https://github.com/invertase/melos)

## Features

- Clean abstraction over GetIt dependency injection
- Automatic disposal of registered instances
- Type-safe dependency registration and retrieval
- Support for custom dispose functions
- Comprehensive error handling
- Easy testing with isolated instances

## Installation 💻

**❗ In order to start using Di you must have the [Flutter SDK][flutter_install_link] installed on your machine.**

Add to your `pubspec.yaml`:

```yaml
dependencies:
  di:
    path: ../../packages/di
```

## Quick Start

```dart
import 'package:di/di.dart';

// Create and initialize DI
final di = GetItDependencyInjection();
await di.init();

// Register a service
final apiService = ApiService('https://api.example.com');
di.register<ApiService>(
  store: apiService,
  dispose: (service) => service.dispose(), // Optional cleanup
);

// Retrieve the service
final service = di.get<ApiService>();

// Check if registered
if (di.has<ApiService>()) {
  print('ApiService is registered');
}

// Clean up
await di.dispose();
```

## API Reference

### Core Methods

#### `init()`
Initialize the dependency injection container.

```dart
await di.init();
```

#### `register<T>()`
Register an instance with optional dispose function.

```dart
di.register<MyService>(
  store: MyService(),
  dispose: (instance) => instance.cleanup(),
);
```

#### `get<T>()`
Retrieve a registered instance.

```dart
final service = di.get<MyService>();
```

#### `has<T>()`
Check if a type is registered.

```dart
if (di.has<MyService>()) {
  // Service is available
}
```

#### `unregister<T>()`
Unregister a specific type and call its dispose function.

```dart
await di.unregister<MyService>();
```

#### `reset()`
Dispose all instances and reinitialize.

```dart
await di.reset();
```

#### `dispose()`
Dispose all instances and clean up.

```dart
await di.dispose();
```

[flutter_install_link]: https://docs.flutter.dev/get-started/install